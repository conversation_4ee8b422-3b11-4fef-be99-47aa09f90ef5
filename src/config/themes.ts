// 皮肤主题配置文件
// 定义不同皮肤的背景图片、背景颜色、边框颜色等属性

// 导入背景图片
import galaxy5 from '@/assets/img/galaxy5.jpg';
import galaxy4 from '@/assets/img/galaxy4.jpg';
import island from '@/assets/img/island.jpg';
import islandDark from '@/assets/img/island_dark.jpg';
import pinkImg from '@/assets/img/pink.jpg';
import yellowImg from '@/assets/img/yellow.jpg';
import greenImg from '@/assets/img/green.jpg';
import blueImg from '@/assets/img/blue.jpg';

export interface IThemeColors {
  // 主色调
  primaryColor: string;
  primaryColorLight: string;
  primaryColorMedium: string;
  primaryColorStrong: string;
  primaryColorTimeStamp: string;

  // 强调色
  accentColor: string;
  accentColorLight: string;
  accentColorMedium: string;
  accentColorStrong: string;

  // 背景色
  bgGlass: string;
  bgGlassHover: string;
  bgGlassPopup: string;
  borderGlass: string;
  borderAccent: string;

  // 阴影
  shadowSoft: string;
  shadowStrong: string;
  shadowAccent: string;

  // 其他颜色
  overlayDark: string;
  bgInput: string;
  bgInputFocus: string;
  placeholderColor: string;
  successColor: string;
  successColorLight: string;
  successColorMedium: string;
  successColorStrong: string;
  errorColor: string;
  errorColorLight: string;
  errorColorMedium: string;
  errorColorStrong: string;

  // 页面文字颜色（用于特定主题的深色字体）
  pageTextPrimary: string;
  pageTextSecondary: string;
  pageTextTertiary: string;

  // PersonDetail组件专用文字颜色
  personDetailTitle: string;
  personDetailTimestamp: string;
  personDetailContext: string;

  // ChatItem组件专用文字颜色
  chatItemUserContext: string;
  chatItemAssistantContext: string;
  chatItemPreResponse: string;
}

export interface IThemeConfig {
  id: string;
  name: string;
  backgroundImage: string;
  colors: IThemeColors;
}

// 默认主题（当前的赛博朋克风格）
export const defaultTheme: IThemeConfig = {
  id: 'cyberpunk',
  name: '赛博朋克',
  backgroundImage: galaxy5,
  colors: {
    // 主色调
    primaryColor: '#00bcd4',
    primaryColorLight: 'rgba(0, 188, 212, 0.1)',
    primaryColorMedium: 'rgba(0, 188, 212, 0.2)',
    primaryColorStrong: 'rgba(0, 188, 212, 0.3)',
    primaryColorTimeStamp: 'rgba(0, 188, 212, 0.8)',

    // 强调色
    accentColor: '#00ffff',
    accentColorLight: 'rgba(0, 255, 255, 0.1)',
    accentColorMedium: 'rgba(0, 255, 255, 0.2)',
    accentColorStrong: 'rgba(0, 255, 255, 0.3)',

    // 背景色 - 恢复原来的深色赛博朋克背景
    bgGlass: 'rgba(1, 28, 32, 0.4)',
    bgGlassHover: 'rgba(1, 28, 32, 0.6)',
    bgGlassPopup: 'rgba(1, 28, 32, 0.4)',
    borderGlass: 'rgba(255, 255, 255, 0.1)',
    borderAccent: 'rgba(0, 255, 255, 0.2)',

    // 阴影 - 恢复原来的霓虹发光效果
    shadowSoft: '0 4px 12px rgba(255, 255, 255, 0.3)',
    shadowStrong: '0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05)',
    shadowAccent: '-4px 0 8px rgba(0, 255, 255, 0.3)',

    // 其他颜色 - 恢复原来的深色覆盖和输入框背景
    overlayDark: 'rgba(0, 0, 0, 0.3)',
    bgInput: 'rgba(255, 255, 255, 0.05)',
    bgInputFocus: 'rgba(255, 255, 255, 0.15)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 保持白色
    pageTextPrimary: '#ffffff',
    pageTextSecondary: 'rgba(255, 255, 255, 0.9)',
    pageTextTertiary: 'rgba(255, 255, 255, 0.8)',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#ffffffff', // 使用强调色作为标题颜色
    personDetailTimestamp: 'rgba(255, 255, 255, 0.7)', // 稍微暗一些的时间戳
    personDetailContext: 'rgba(255, 255, 255, 0.9)', // 内容文字保持较高可读性

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#ffffff', // 用户消息保持纯白
    chatItemAssistantContext: 'rgba(255, 255, 255, 0.95)', // AI回复稍微柔和一些
    chatItemPreResponse: 'rgba(0, 255, 255, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
  },
};

// 星空主题
export const starryTheme: IThemeConfig = {
  id: 'starry',
  name: '星空',
  backgroundImage: galaxy4,
  colors: {
    // 主色调 - 紫色系
    primaryColor: '#8b5cf6',
    primaryColorLight: 'rgba(139, 92, 246, 0.1)',
    primaryColorMedium: 'rgba(139, 92, 246, 0.2)',
    primaryColorStrong: 'rgba(139, 92, 246, 0.3)',
    primaryColorTimeStamp: 'rgba(139, 92, 246, 0.8)',

    // 强调色 - 亮紫色
    accentColor: '#a855f7',
    accentColorLight: 'rgba(168, 85, 247, 0.1)',
    accentColorMedium: 'rgba(168, 85, 247, 0.2)',
    accentColorStrong: 'rgba(168, 85, 247, 0.3)',

    // 背景色 - 深紫色调
    bgGlass: 'rgba(88, 28, 135, 0.25)',
    bgGlassHover: 'rgba(88, 28, 135, 0.35)',
    bgGlassPopup: 'rgba(88, 28, 135, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(168, 85, 247, 0.3)',

    // 阴影 - 紫色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(168, 85, 247, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 保持白色
    pageTextPrimary: '#ffffff',
    pageTextSecondary: 'rgba(255, 255, 255, 0.9)',
    pageTextTertiary: 'rgba(255, 255, 255, 0.8)',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#a855f7', // 使用强调色作为标题颜色
    personDetailTimestamp: 'rgba(255, 255, 255, 0.7)', // 稍微暗一些的时间戳
    personDetailContext: 'rgba(255, 255, 255, 0.9)', // 内容文字保持较高可读性

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#ffffff', // 用户消息保持纯白
    chatItemAssistantContext: 'rgba(255, 255, 255, 0.95)', // AI回复稍微柔和一些
    chatItemPreResponse: 'rgba(168, 85, 247, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
  },
};

// 海岛主题
export const islandTheme: IThemeConfig = {
  id: 'island',
  name: '海岛',
  backgroundImage: island,
  colors: {
    // 主色调 - 蓝绿色系
    primaryColor: '#06b6d4',
    primaryColorLight: 'rgba(6, 182, 212, 0.1)',
    primaryColorMedium: 'rgba(6, 182, 212, 0.2)',
    primaryColorStrong: 'rgba(6, 182, 212, 0.3)',
    primaryColorTimeStamp: 'rgba(6, 182, 212, 0.8)',

    // 强调色 - 翠绿色
    accentColor: '#10b981',
    accentColorLight: 'rgba(16, 185, 129, 0.1)',
    accentColorMedium: 'rgba(16, 185, 129, 0.2)',
    accentColorStrong: 'rgba(16, 185, 129, 0.3)',

    // 背景色 - 海洋蓝调
    bgGlass: 'rgba(207, 20, 166, 0.5)',
    bgGlassHover: 'rgba(14, 116, 144, 0.35)',
    bgGlassPopup: 'rgba(14, 116, 144, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(16, 185, 129, 0.3)',

    // 阴影 - 蓝绿调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(16, 185, 129, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#10b981', // 使用强调色作为标题颜色
    personDetailTimestamp: '#9ca3af', // 使用次要文字颜色
    personDetailContext: '#6b7280', // 使用主要文字颜色

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#374151', // 用户消息使用深色
    chatItemAssistantContext: '#4b5563', // AI回复使用稍浅的深色
    chatItemPreResponse: 'rgba(16, 185, 129, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 暗夜海岛主题
export const darkIslandTheme: IThemeConfig = {
  id: 'dark-island',
  name: '暗夜海岛',
  backgroundImage: islandDark,
  colors: {
    // 主色调 - 深蓝色系
    primaryColor: '#1e40af',
    primaryColorLight: 'rgba(30, 64, 175, 0.1)',
    primaryColorMedium: 'rgba(30, 64, 175, 0.2)',
    primaryColorStrong: 'rgba(30, 64, 175, 0.3)',
    primaryColorTimeStamp: 'rgba(30, 64, 175, 0.8)',

    // 强调色 - 深青色
    accentColor: '#0891b2',
    accentColorLight: 'rgba(8, 145, 178, 0.1)',
    accentColorMedium: 'rgba(8, 145, 178, 0.2)',
    accentColorStrong: 'rgba(8, 145, 178, 0.3)',

    // 背景色 - 深海蓝调
    bgGlass: 'rgba(30, 58, 138, 0.25)',
    bgGlassHover: 'rgba(30, 58, 138, 0.35)',
    bgGlassPopup: 'rgba(30, 58, 138, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(8, 145, 178, 0.3)',

    // 阴影 - 深蓝调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(8, 145, 178, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 保持白色
    pageTextPrimary: '#ffffff',
    pageTextSecondary: 'rgba(255, 255, 255, 0.9)',
    pageTextTertiary: 'rgba(255, 255, 255, 0.8)',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#0891b2', // 使用强调色作为标题颜色
    personDetailTimestamp: 'rgba(255, 255, 255, 0.7)', // 稍微暗一些的时间戳
    personDetailContext: 'rgba(255, 255, 255, 0.9)', // 内容文字保持较高可读性

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#ffffff', // 用户消息保持纯白
    chatItemAssistantContext: 'rgba(255, 255, 255, 0.95)', // AI回复稍微柔和一些
    chatItemPreResponse: 'rgba(8, 145, 178, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
  },
};

// 粉色主题
export const pinkTheme: IThemeConfig = {
  id: 'pink',
  name: '粉色梦境',
  backgroundImage: pinkImg,
  colors: {
    // 主色调 - 粉色系
    primaryColor: '#ec4899',
    primaryColorLight: 'rgba(236, 72, 153, 0.1)',
    primaryColorMedium: 'rgba(236, 72, 153, 0.2)',
    primaryColorStrong: 'rgba(236, 72, 153, 0.3)',
    primaryColorTimeStamp: 'rgba(236, 72, 153, 0.8)',

    // 强调色 - 亮粉色
    accentColor: '#f472b6',
    accentColorLight: 'rgba(244, 114, 182, 0.1)',
    accentColorMedium: 'rgba(244, 114, 182, 0.2)',
    accentColorStrong: 'rgba(244, 114, 182, 0.3)',

    // 背景色 - 深粉色调，降低透明度
    bgGlass: 'rgba(252, 220, 235, 0.9)',
    bgGlassHover: 'rgba(131, 24, 67, 0.35)',
    bgGlassPopup: 'rgba(131, 24, 67, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(244, 114, 182, 0.5)',

    // 阴影 - 粉色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(244, 114, 182, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#000000f6', // 使用强调色作为标题颜色
    personDetailTimestamp: '#36383cff', // 使用次要文字颜色
    personDetailContext: '#747881ff', // 使用主要文字颜色

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#374151', // 用户消息使用深色
    chatItemAssistantContext: '#4b5563', // AI回复使用稍浅的深色
    chatItemPreResponse: 'rgba(120, 107, 113, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色 - 浅色
    placeholderColor: '#6b7280',
  },
};

// 黄色主题
export const yellowTheme: IThemeConfig = {
  id: 'yellow',
  name: '金色阳光',
  backgroundImage: yellowImg,
  colors: {
    // 主色调 - 黄色系
    primaryColor: '#DFA803ff',
    primaryColorLight: 'rgba(223, 168, 3, 0.1)',
    primaryColorMedium: 'rgba(223, 168, 3, 0.2)',
    primaryColorStrong: 'rgba(223, 168, 3, 0.3)',
    primaryColorTimeStamp: 'rgba(223, 168, 3, 0.8)',

    // 强调色 - 亮黄色
    accentColor: '#facc15',
    accentColorLight: 'rgba(250, 204, 21, 0.1)',
    accentColorMedium: 'rgba(250, 204, 21, 0.2)',
    accentColorStrong: 'rgba(250, 204, 21, 0.3)',

    // 背景色 - 深黄色调，降低透明度
    bgGlass: 'rgba(247, 235, 201, 0.9)',
    bgGlassHover: 'rgba(120, 53, 15, 0.35)',
    bgGlassPopup: 'rgba(120, 53, 15, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(250, 204, 21, 0.5)',

    // 阴影 - 黄色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(250, 204, 21, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#000000f6', // 使用强调色作为标题颜色
    personDetailTimestamp: '#36383cff', // 使用次要文字颜色
    personDetailContext: '#747881ff', // 使用主要文字颜色

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#374151', // 用户消息使用深色
    chatItemAssistantContext: '#4b5563', // AI回复使用稍浅的深色
    chatItemPreResponse: 'rgba(95, 93, 85, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色 - 浅色
    placeholderColor: '#6b7280',
  },
};

// 绿色主题
export const greenTheme: IThemeConfig = {
  id: 'green',
  name: '翠绿森林',
  backgroundImage: greenImg,
  colors: {
    // 主色调 - 绿色系
    primaryColor: '#16a34a',
    primaryColorLight: 'rgba(22, 163, 74, 0.1)',
    primaryColorMedium: 'rgba(22, 163, 74, 0.2)',
    primaryColorStrong: 'rgba(22, 163, 74, 0.3)',
    primaryColorTimeStamp: 'rgba(22, 163, 74, 0.8)',

    // 强调色 - 亮绿色
    accentColor: '#22c55e',
    accentColorLight: 'rgba(34, 197, 94, 0.1)',
    accentColorMedium: 'rgba(34, 197, 94, 0.2)',
    accentColorStrong: 'rgba(34, 197, 94, 0.3)',

    // 背景色 - 深绿色调
    bgGlass: 'rgba(20, 83, 45, 0.25)',
    bgGlassHover: 'rgba(20, 83, 45, 0.35)',
    bgGlassPopup: 'rgba(20, 83, 45, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(34, 197, 94, 0.3)',

    // 阴影 - 绿色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(34, 197, 94, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#22c55e', // 使用强调色作为标题颜色
    personDetailTimestamp: '#9ca3af', // 使用次要文字颜色
    personDetailContext: '#6b7280', // 使用主要文字颜色

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#374151', // 用户消息使用深色
    chatItemAssistantContext: '#4b5563', // AI回复使用稍浅的深色
    chatItemPreResponse: 'rgba(34, 197, 94, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色 - 深色
    placeholderColor: '#9ca3af',
  },
};

// 蓝色主题
export const blueTheme: IThemeConfig = {
  id: 'blue',
  name: '深海蓝调',
  backgroundImage: blueImg,
  colors: {
    // 主色调 - 蓝色系
    primaryColor: '#467ceeff',
    primaryColorLight: 'rgba(37, 99, 235, 0.1)',
    primaryColorMedium: 'rgba(37, 99, 235, 0.2)',
    primaryColorStrong: 'rgba(37, 99, 235, 0.3)',
    primaryColorTimeStamp: 'rgba(70, 124, 238, 0.8)',

    // 强调色 - 亮蓝色
    accentColor: '#3b82f6',
    accentColorLight: 'rgba(59, 130, 246, 0.1)',
    accentColorMedium: 'rgba(59, 130, 246, 0.2)',
    accentColorStrong: 'rgba(59, 130, 246, 0.3)',

    // 背景色 - 深蓝色调，降低透明度
    bgGlass: 'rgba(216, 232, 253, 0.9)',
    bgGlassHover: 'rgba(30, 58, 138, 0.35)',
    bgGlassPopup: 'rgba(30, 58, 138, 0.35)',
    borderGlass: 'rgba(255, 255, 255, 0.2)',
    borderAccent: 'rgba(59, 130, 246, 0.5)',

    // 阴影 - 蓝色调
    shadowSoft: '0 8px 32px rgba(0, 0, 0, 0.2)',
    shadowStrong: '0 20px 60px rgba(0, 0, 0, 0.3)',
    shadowAccent: '0 0 20px rgba(59, 130, 246, 0.2)',

    // 其他颜色
    overlayDark: 'rgba(0, 0, 0, 0.15)',
    bgInput: 'rgba(255, 255, 255, 0.08)',
    bgInputFocus: 'rgba(255, 255, 255, 0.12)',
    successColor: '#22c55e',
    successColorLight: 'rgba(34, 197, 94, 0.1)',
    successColorMedium: 'rgba(34, 197, 94, 0.2)',
    successColorStrong: 'rgba(34, 197, 94, 0.3)',
    errorColor: '#ef4444',
    errorColorLight: 'rgba(239, 68, 68, 0.1)',
    errorColorMedium: 'rgba(239, 68, 68, 0.2)',
    errorColorStrong: 'rgba(239, 68, 68, 0.3)',

    // 页面文字颜色 - 使用深色（调浅）
    pageTextPrimary: '#6b7280',
    pageTextSecondary: '#9ca3af',
    pageTextTertiary: '#d1d5db',

    // PersonDetail组件专用文字颜色
    personDetailTitle: '#3b82f6', // 使用强调色作为标题颜色
    personDetailTimestamp: '#7c7f85ff', // 使用时间戳
    personDetailContext: '#515560ff', // 使用主要文字颜色

    // ChatItem组件专用文字颜色
    chatItemUserContext: '#374151', // 用户消息使用深色
    chatItemAssistantContext: '#4b5563', // AI回复使用稍浅的深色
    chatItemPreResponse: 'rgba(100, 107, 119, 0.8)', // 预响应使用强调色但透明度较低

    // 输入框placeholder颜色 - 浅色
    placeholderColor: '#6b7280',
  },
};

// 所有主题（包括隐藏的）
export const allThemes: IThemeConfig[] = [
  defaultTheme,
  starryTheme,
  islandTheme,
  darkIslandTheme,
  pinkTheme,
  yellowTheme,
  greenTheme,
  blueTheme,
];

// 隐藏的主题ID列表
const hiddenThemeIds = ['starry', 'island', 'dark-island', 'green'];

// 当前可用主题（排除隐藏的主题）
export const availableThemes: IThemeConfig[] = allThemes.filter((theme) => !hiddenThemeIds.includes(theme.id));

// 根据ID获取主题（从所有主题中查找，包括隐藏的）
export function getThemeById(id: string): IThemeConfig | undefined {
  return allThemes.find((theme) => theme.id === id);
}

// 获取默认主题
export function getDefaultTheme(): IThemeConfig {
  return defaultTheme;
}
