// 导入默认头像
import defaultAvatar from '@/assets/icon/user_avatar.png';

// 新的默认头像URL列表 - 24个头像按用户提供的顺序
const defaultAvatarUrls = [
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midwoman1.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midwoman2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midman1.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midman2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/boy2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/man2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/woman1.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/woman2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/woman3.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/woman4.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/girl2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/girl3.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandpa1.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandpa3.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandma1.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandma2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandpa2.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandpa4.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandma3.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/grandma4.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/man1.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/man3.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midman3.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midwoman4.jpg',
  'https://s3plus.meituan.net/aigc-media-resources/relationship/%E5%A4%B4%E5%9E%8B%E6%96%B0/midwoman3.jpg',
];

// 默认头像映射 - 支持24个头像
const defaultAvatarMap: Record<string, string> = {
  default_avatar_1: defaultAvatarUrls[0],
  default_avatar_2: defaultAvatarUrls[1],
  default_avatar_3: defaultAvatarUrls[2],
  default_avatar_4: defaultAvatarUrls[3],
  default_avatar_5: defaultAvatarUrls[4],
  default_avatar_6: defaultAvatarUrls[5],
  default_avatar_7: defaultAvatarUrls[6],
  default_avatar_8: defaultAvatarUrls[7],
  default_avatar_9: defaultAvatarUrls[8],
  default_avatar_10: defaultAvatarUrls[9],
  default_avatar_11: defaultAvatarUrls[10],
  default_avatar_12: defaultAvatarUrls[11],
  default_avatar_13: defaultAvatarUrls[12],
  default_avatar_14: defaultAvatarUrls[13],
  default_avatar_15: defaultAvatarUrls[14],
  default_avatar_16: defaultAvatarUrls[15],
  default_avatar_17: defaultAvatarUrls[16],
  default_avatar_18: defaultAvatarUrls[17],
  default_avatar_19: defaultAvatarUrls[18],
  default_avatar_20: defaultAvatarUrls[19],
  default_avatar_21: defaultAvatarUrls[20],
  default_avatar_22: defaultAvatarUrls[21],
  default_avatar_23: defaultAvatarUrls[22],
  default_avatar_24: defaultAvatarUrls[23],
};

/**
 * 根据人员ID获取随机默认头像ID
 * @param personId 人员ID，用于确保同一人员总是获得相同的头像
 * @returns 默认头像ID
 */
export function getRandomDefaultAvatarId(personId: string): string {
  const avatarIds = Object.keys(defaultAvatarMap);
  // 使用人员ID的简单哈希值来确保同一人员总是获得相同的头像
  let hash = 0;
  for (let i = 0; i < personId.length; i++) {
    const char = personId.charCodeAt(i);
    hash = (hash * 31 + char) % 1000000; // 使用简单的乘法和取模运算
  }
  const index = Math.abs(hash) % avatarIds.length;
  return avatarIds[index];
}

/**
 * 根据头像标识符获取头像URL
 * @param avatarId 头像标识符，可能是默认头像ID或自定义头像URL
 * @param personId 人员ID，当avatarId为空时用于生成随机默认头像
 * @returns 头像URL
 */
export function getAvatarUrl(avatarId: string | null | undefined, personId?: string): string {
  if (!avatarId) {
    // 如果没有头像且有人员ID，返回随机默认头像
    if (personId) {
      const randomAvatarId = getRandomDefaultAvatarId(personId);
      return defaultAvatarMap[randomAvatarId];
    }
    return defaultAvatar;
  }

  // 如果是默认头像ID，返回对应的本地图片
  if (defaultAvatarMap[avatarId]) {
    return defaultAvatarMap[avatarId];
  }

  // 如果是自定义头像URL，直接返回
  if (avatarId.startsWith('http') || avatarId.startsWith('data:') || avatarId.startsWith('blob:')) {
    return avatarId;
  }

  // 其他情况返回默认头像
  return defaultAvatar;
}

/**
 * 检查是否为默认头像
 * @param avatarId 头像标识符
 * @returns 是否为默认头像
 */
export function isDefaultAvatar(avatarId: string | null | undefined): boolean {
  return !!(avatarId && defaultAvatarMap[avatarId]);
}

/**
 * 获取所有默认头像列表
 * @returns 默认头像列表
 */
export function getDefaultAvatars() {
  return [
    { id: 'default_avatar_1', src: defaultAvatarUrls[0] },
    { id: 'default_avatar_2', src: defaultAvatarUrls[1] },
    { id: 'default_avatar_3', src: defaultAvatarUrls[2] },
    { id: 'default_avatar_4', src: defaultAvatarUrls[3] },
    { id: 'default_avatar_5', src: defaultAvatarUrls[4] },
    { id: 'default_avatar_6', src: defaultAvatarUrls[5] },
    { id: 'default_avatar_7', src: defaultAvatarUrls[6] },
    { id: 'default_avatar_8', src: defaultAvatarUrls[7] },
    { id: 'default_avatar_9', src: defaultAvatarUrls[8] },
    { id: 'default_avatar_10', src: defaultAvatarUrls[9] },
    { id: 'default_avatar_11', src: defaultAvatarUrls[10] },
    { id: 'default_avatar_12', src: defaultAvatarUrls[11] },
    { id: 'default_avatar_13', src: defaultAvatarUrls[12] },
    { id: 'default_avatar_14', src: defaultAvatarUrls[13] },
    { id: 'default_avatar_15', src: defaultAvatarUrls[14] },
    { id: 'default_avatar_16', src: defaultAvatarUrls[15] },
    { id: 'default_avatar_17', src: defaultAvatarUrls[16] },
    { id: 'default_avatar_18', src: defaultAvatarUrls[17] },
    { id: 'default_avatar_19', src: defaultAvatarUrls[18] },
    { id: 'default_avatar_20', src: defaultAvatarUrls[19] },
    { id: 'default_avatar_21', src: defaultAvatarUrls[20] },
    { id: 'default_avatar_22', src: defaultAvatarUrls[21] },
    { id: 'default_avatar_23', src: defaultAvatarUrls[22] },
    { id: 'default_avatar_24', src: defaultAvatarUrls[23] },
  ];
}

/**
 * 获取分页的默认头像列表，每页8个头像
 * @returns 分页的头像列表，共3页
 */
export function getDefaultAvatarsByPages() {
  const allAvatars = getDefaultAvatars();
  const pageSize = 8;
  const pages = [];

  for (let i = 0; i < allAvatars.length; i += pageSize) {
    pages.push(allAvatars.slice(i, i + pageSize));
  }

  return pages;
}
