<template>
  <div class="topic-container">
    <!-- 推荐话题 -->
    <div class="topic-section">
      <div class="section-header">
        <span class="section-icon">💬</span>
        <span class="section-title">推荐话题</span>
        <div class="section-actions">
          <button class="section-tts-btn" title="朗读内容" @click="handleTtsClick">
            <TrumpetIcon class="section-tts-icon" :size="'32px'" />
          </button>
          <button class="refresh-btn" title="刷新话题" :disabled="loadingTopics" @click="handleRefreshTopics">
            <RefreshIcon class="refresh-icon" width="32px" height="32px" />
          </button>
        </div>
      </div>
      <div class="section-content">
        <div v-if="loadingTopics" class="loading-text">加载中...</div>
        <div v-else-if="topics.length > 0" class="topic-content">
          <div v-for="(topic, index) in topics" :key="index" class="topic-item" @click="handleTopicClick(topic)">
            <div class="topic-text">{{ topic.topic }}</div>
          </div>
        </div>
        <div v-else class="topic-content">
          <div class="empty-topic">暂无推荐话题，快去和ta聊聊天吧！</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import type { IRecommendedTopic } from '@/apis/memory';
import { getRecommendTopics } from '@/apis/memory';
import { useAudioQueue } from '@/pages/Chat/useAudioPlayer';
import TrumpetIcon from '@/assets/icons/TrumpetIcon.vue';
import RefreshIcon from '@/assets/icons/RefreshIcon.vue';

// Props定义
interface IProps {
  personId: string;
  userId: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  topicClick: [topic: IRecommendedTopic];
}>();

// TTS相关
const { play, stop, isCurrentAudioPlaying, audioStatus } = useAudioQueue();
const isTtsPlaying = ref(false);
const ttsId = 'topic-section-tts';

// 响应式数据
const loadingTopics = ref(false);
const topics = ref<IRecommendedTopic[]>([]);

// 处理话题点击
const handleTopicClick = (topic: IRecommendedTopic) => {
  emit('topicClick', topic);
};

// TTS朗读处理
const handleTtsClick = () => {
  if (isCurrentAudioPlaying(ttsId)) {
    stop();
    isTtsPlaying.value = false;
  } else {
    // 构建朗读内容：读出推荐的两个话题
    const ttsContent = topics.value.map((topic) => topic.topic).join('。');

    if (ttsContent.trim()) {
      isTtsPlaying.value = true;
      play({
        id: ttsId,
        text: ttsContent,
        type: 'manualPlay',
      });

      // 监听播放状态变化
      const checkPlayingStatus = () => {
        if (!isCurrentAudioPlaying(ttsId) && audioStatus.value === 'completed') {
          isTtsPlaying.value = false;
        } else {
          setTimeout(checkPlayingStatus, 100);
        }
      };
      checkPlayingStatus();
    }
  }
};

// 加载推荐话题
const loadRecommendedTopics = async () => {
  try {
    loadingTopics.value = true;
    console.log('🔄 [TopicSection] 开始获取推荐话题...', {
      personId: props.personId,
      userId: props.userId,
    });

    const response = await getRecommendTopics({
      user_id: props.userId,
      person_id: props.personId,
      max_topics: 2,
      fast_mode: false,
    });
    console.log('✅ [TopicSection] 推荐话题获取成功:', response);

    if (response && response.result === 'success' && response.recommended_topics) {
      // 过滤掉topic为"```json"的推荐话题
      const validTopics = response.recommended_topics.filter(
        (topic) => topic.topic && topic.topic.trim() !== '```json',
      );
      topics.value = validTopics;
    } else {
      topics.value = [];
    }
  } catch (error) {
    console.error('❌ [TopicSection] 推荐话题获取失败:', error);
    topics.value = [];
  } finally {
    loadingTopics.value = false;
  }
};

// 处理刷新话题
const handleRefreshTopics = () => {
  console.log('🔄 [TopicSection] 刷新推荐话题');
  void loadRecommendedTopics();
};

// 组件挂载时加载数据
onMounted(() => {
  void loadRecommendedTopics();
});
</script>

<style lang="scss" scoped>
.topic-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.topic-section {
  border: none;
  border-radius: 16px;
  padding: 22px 22px 22px 28px; /* 增加左侧padding从22px到28px */
  margin-top: 24px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px;
  }

  .section-title {
    color: var(--primary-color);
    font-size: 34px;
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px;
  }

  .refresh-btn,
  .section-tts-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    border: 2px solid var(--primary-color);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .refresh-icon,
    .section-tts-icon {
      width: 32px;
      height: 32px;
      min-width: 32px;
      min-height: 32px;
    }
  }
}

.section-content {
  color: var(--person-detail-context); // 使用PersonDetail专用内容颜色
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: var(--person-detail-context);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px;
}

.topic-content {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .topic-item {
    background: var(--primary-color-light);
    border: 2px solid var(--border-accent);
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;

    .topic-text {
      color: var(--person-detail-context);
      font-size: 32px;
      font-weight: 450;
      line-height: 1.4;
    }
  }

  .empty-topic {
    color: var(--person-detail-context);
    font-size: 30px;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: var(--primary-color-light);
    border: 1px dashed var(--border-accent);
    border-radius: 12px;
    line-height: 1.6;
  }
}
</style>
